import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';

interface Heading1Props extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

const Heading1 = forwardRef<HTMLHeadingElement, Heading1Props>(
  ({ className, children, ...props }, ref) => {
    return (
      <h1
        ref={ref}
        className={className}
        style={{
          fontFamily: '"Inter", sans-serif',
          fontSize: '48px',
          fontWeight: 800,
          lineHeight: 1.2,
        }}
        {...props}
      >
        {children}
      </h1>
    );
  }
);

Heading1.displayName = 'Heading1';

export { Heading1 };
