import { forwardRef } from 'react';
import type { HTMLAttributes, ReactNode } from 'react';

interface BodyTextProps extends HTMLAttributes<HTMLParagraphElement> {
  children: ReactNode;
}

const BodyText = forwardRef<HTMLParagraphElement, BodyTextProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={className}
        style={{
          fontFamily: '"Inter", sans-serif',
          fontSize: '16px',
          fontWeight: 400,
          lineHeight: 1.5,
        }}
        {...props}
      >
        {children}
      </p>
    );
  }
);

BodyText.displayName = 'BodyText';

export { BodyText };
